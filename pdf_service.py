"""
PDF Service - Handle PDF upload, processing, and text extraction
"""
import os
import hashlib
import uuid
import asyncio
from typing import List, Dict, Optional
import aiofiles
from fastapi import UploadFile, HTTPException
import PyPDF2
import pdfplumber
from io import BytesIO
import google.generativeai as genai
import numpy as np
import faiss
import json
from dotenv import load_dotenv

load_dotenv()

class PDFService:
    """Service for handling PDF operations"""
    
    def __init__(self):
        self.upload_path = os.getenv("DOCS_UPLOAD_PATH", "./data/docs")
        self.vector_db_path = os.getenv("VECTOR_DB_PATH", "./vector_db")
        self.max_file_size = int(os.getenv("MAX_FILE_SIZE", 10485760))  # 10MB
        self.chunk_size = 1000
        self.chunk_overlap = 200
        
        # Initialize Google AI for embeddings
        genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
        
        # Ensure directories exist
        os.makedirs(self.upload_path, exist_ok=True)
        os.makedirs(self.vector_db_path, exist_ok=True)
        
        # Initialize or load FAISS index
        self.dimension = 768  # Google embedding dimension
        self.index = None
        self.documents_metadata = []
        self._load_or_create_index()
        
        print("✅ PDFService initialized")
    
    def _load_or_create_index(self):
        """Load existing FAISS index or create new one"""
        try:
            index_path = os.path.join(self.vector_db_path, "faiss_index.bin")
            metadata_path = os.path.join(self.vector_db_path, "documents_metadata.json")
            
            if os.path.exists(index_path) and os.path.exists(metadata_path):
                # Load existing index
                self.index = faiss.read_index(index_path)
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    self.documents_metadata = json.load(f)
                print(f"✅ Loaded existing FAISS index with {self.index.ntotal} vectors")
            else:
                # Create new index
                self.index = faiss.IndexFlatL2(self.dimension)
                self.documents_metadata = []
                print("✅ Created new FAISS index")
                
        except Exception as e:
            print(f"⚠️ Error loading FAISS index, creating new one: {e}")
            self.index = faiss.IndexFlatL2(self.dimension)
            self.documents_metadata = []
    
    def _save_index(self):
        """Save FAISS index and metadata"""
        try:
            index_path = os.path.join(self.vector_db_path, "faiss_index.bin")
            metadata_path = os.path.join(self.vector_db_path, "documents_metadata.json")
            
            faiss.write_index(self.index, index_path)
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(self.documents_metadata, f, ensure_ascii=False, indent=2)
            
            print(f"✅ Saved FAISS index with {self.index.ntotal} vectors")
        except Exception as e:
            print(f"❌ Error saving FAISS index: {e}")
    
    async def upload_pdf(self, file: UploadFile) -> Dict:
        """Upload and process PDF file"""
        try:
            # Validate file
            if not file.filename.lower().endswith('.pdf'):
                raise HTTPException(status_code=400, detail="Only PDF files are allowed")
            
            # Read file content
            content = await file.read()
            
            # Check file size
            if len(content) > self.max_file_size:
                raise HTTPException(
                    status_code=400, 
                    detail=f"File too large. Maximum size is {self.max_file_size / 1024 / 1024:.1f}MB"
                )
            
            # Generate file hash for deduplication
            file_hash = hashlib.sha256(content).hexdigest()
            
            # Check if file already exists
            existing_doc = self._find_document_by_hash(file_hash)
            if existing_doc:
                return {
                    "message": "Document already exists",
                    "document_id": existing_doc["id"],
                    "filename": existing_doc["filename"],
                    "status": "duplicate"
                }
            
            # Generate unique filename
            file_id = str(uuid.uuid4())
            safe_filename = f"{file_id}_{file.filename}"
            file_path = os.path.join(self.upload_path, safe_filename)
            
            # Save file
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(content)
            
            # Extract text from PDF
            text_content = await self._extract_text_from_pdf(content)
            
            if not text_content.strip():
                # Clean up file if no text extracted
                os.remove(file_path)
                raise HTTPException(status_code=400, detail="No text content found in PDF")
            
            # Create document metadata
            document = {
                "id": file_id,
                "filename": safe_filename,
                "original_filename": file.filename,
                "file_path": file_path,
                "file_size": len(content),
                "content_hash": file_hash,
                "text_content": text_content,
                "processed": False,
                "chunks": []
            }
            
            # Process document (chunk and vectorize)
            await self._process_document(document)
            
            # Add to metadata
            self.documents_metadata.append(document)
            
            # Save index
            self._save_index()
            
            return {
                "message": "PDF uploaded and processed successfully",
                "document_id": file_id,
                "filename": file.filename,
                "file_size": len(content),
                "chunks_created": len(document["chunks"]),
                "status": "success"
            }
            
        except HTTPException:
            raise
        except Exception as e:
            print(f"Error uploading PDF: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to upload PDF: {str(e)}")
    
    async def _extract_text_from_pdf(self, content: bytes) -> str:
        """Extract text from PDF using multiple methods"""
        text_content = ""
        
        try:
            # Method 1: Try pdfplumber first (better for complex layouts)
            with pdfplumber.open(BytesIO(content)) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text_content += page_text + "\n"
            
            # If pdfplumber didn't work well, try PyPDF2
            if len(text_content.strip()) < 100:
                text_content = ""
                pdf_reader = PyPDF2.PdfReader(BytesIO(content))
                for page in pdf_reader.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text_content += page_text + "\n"
            
        except Exception as e:
            print(f"Error extracting text from PDF: {e}")
            raise HTTPException(status_code=400, detail="Failed to extract text from PDF")
        
        return text_content.strip()
    
    async def _process_document(self, document: Dict):
        """Process document: chunk text and create embeddings"""
        try:
            text_content = document["text_content"]
            
            # Create text chunks
            chunks = self._create_text_chunks(text_content)
            
            # Generate embeddings for chunks
            for i, chunk_text in enumerate(chunks):
                try:
                    # Generate embedding using Google AI
                    embedding = await self._generate_embedding(chunk_text)
                    
                    if embedding is not None:
                        # Add to FAISS index
                        embedding_array = np.array([embedding], dtype='float32')
                        self.index.add(embedding_array)
                        
                        # Store chunk metadata
                        chunk_metadata = {
                            "chunk_id": str(uuid.uuid4()),
                            "document_id": document["id"],
                            "chunk_index": i,
                            "content": chunk_text,
                            "vector_index": self.index.ntotal - 1
                        }
                        
                        document["chunks"].append(chunk_metadata)
                
                except Exception as e:
                    print(f"Error processing chunk {i}: {e}")
                    continue
            
            document["processed"] = True
            print(f"✅ Processed document with {len(document['chunks'])} chunks")
            
        except Exception as e:
            print(f"Error processing document: {e}")
            raise
    
    def _create_text_chunks(self, text: str) -> List[str]:
        """Split text into overlapping chunks"""
        chunks = []
        words = text.split()
        
        for i in range(0, len(words), self.chunk_size - self.chunk_overlap):
            chunk_words = words[i:i + self.chunk_size]
            chunk_text = ' '.join(chunk_words)
            
            if chunk_text.strip():
                chunks.append(chunk_text.strip())
        
        return chunks
    
    async def _generate_embedding(self, text: str) -> Optional[List[float]]:
        """Generate embedding for text using Google AI"""
        try:
            # Use Google's embedding model
            result = await asyncio.to_thread(
                genai.embed_content,
                model="models/embedding-001",
                content=text,
                task_type="retrieval_document"
            )
            return result['embedding']
        except Exception as e:
            print(f"Error generating embedding: {e}")
            return None
    
    async def search_documents(self, query: str, top_k: int = 5) -> List[Dict]:
        """Search documents using vector similarity"""
        try:
            if self.index.ntotal == 0:
                return []
            
            # Generate query embedding
            query_embedding = await self._generate_embedding(query)
            if query_embedding is None:
                return []
            
            # Search in FAISS index
            query_vector = np.array([query_embedding], dtype='float32')
            distances, indices = self.index.search(query_vector, min(top_k, self.index.ntotal))
            
            # Retrieve matching chunks
            results = []
            for distance, idx in zip(distances[0], indices[0]):
                if idx >= 0:  # Valid index
                    # Find chunk metadata
                    chunk_metadata = self._find_chunk_by_vector_index(idx)
                    if chunk_metadata:
                        results.append({
                            "chunk_id": chunk_metadata["chunk_id"],
                            "document_id": chunk_metadata["document_id"],
                            "content": chunk_metadata["content"],
                            "similarity_score": float(1 / (1 + distance)),  # Convert distance to similarity
                            "document_name": self._get_document_name(chunk_metadata["document_id"])
                        })
            
            return results
            
        except Exception as e:
            print(f"Error searching documents: {e}")
            return []
    
    def _find_document_by_hash(self, file_hash: str) -> Optional[Dict]:
        """Find document by content hash"""
        for doc in self.documents_metadata:
            if doc.get("content_hash") == file_hash:
                return doc
        return None
    
    def _find_chunk_by_vector_index(self, vector_index: int) -> Optional[Dict]:
        """Find chunk metadata by vector index"""
        for doc in self.documents_metadata:
            for chunk in doc.get("chunks", []):
                if chunk.get("vector_index") == vector_index:
                    return chunk
        return None
    
    def _get_document_name(self, document_id: str) -> str:
        """Get document name by ID"""
        for doc in self.documents_metadata:
            if doc["id"] == document_id:
                return doc["original_filename"]
        return "Unknown Document"
    
    def list_documents(self) -> List[Dict]:
        """List all uploaded documents"""
        return [
            {
                "id": doc["id"],
                "filename": doc["original_filename"],
                "file_size": doc["file_size"],
                "chunks_count": len(doc.get("chunks", [])),
                "processed": doc.get("processed", False)
            }
            for doc in self.documents_metadata
        ]
    
    async def delete_document(self, document_id: str) -> bool:
        """Delete document and its vectors"""
        try:
            # Find document
            doc_to_remove = None
            for i, doc in enumerate(self.documents_metadata):
                if doc["id"] == document_id:
                    doc_to_remove = (i, doc)
                    break
            
            if not doc_to_remove:
                return False
            
            doc_index, document = doc_to_remove
            
            # Remove file
            if os.path.exists(document["file_path"]):
                os.remove(document["file_path"])
            
            # Remove from metadata
            self.documents_metadata.pop(doc_index)
            
            # Rebuild FAISS index (since FAISS doesn't support deletion)
            await self._rebuild_index()
            
            return True
            
        except Exception as e:
            print(f"Error deleting document: {e}")
            return False
    
    async def _rebuild_index(self):
        """Rebuild FAISS index after deletion"""
        try:
            # Create new index
            self.index = faiss.IndexFlatL2(self.dimension)
            
            # Re-add all chunks
            for doc in self.documents_metadata:
                for chunk in doc.get("chunks", []):
                    embedding = await self._generate_embedding(chunk["content"])
                    if embedding:
                        embedding_array = np.array([embedding], dtype='float32')
                        self.index.add(embedding_array)
                        chunk["vector_index"] = self.index.ntotal - 1
            
            # Save updated index
            self._save_index()
            
        except Exception as e:
            print(f"Error rebuilding index: {e}")
