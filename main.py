"""
FastAPI Chatbot Backend with Gemini AI, PostgreSQL, and Vector Search
"""
import os
import uuid
import uvicorn
from fastapi import FastAPI, HTTPException, Request, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from pydantic import BaseModel
from typing import Optional, List
import socketio
from dotenv import load_dotenv
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

from database import init_db, close_db
from chat_service import ChatService
from pdf_service import PDFService
from check_models import run_all_health_checks

# Load environment variables
load_dotenv()

# Create data directories if they don't exist
os.makedirs("data/docs", exist_ok=True)
os.makedirs("vector_db", exist_ok=True)

# Rate limiting
limiter = Limiter(key_func=get_remote_address)

# Initialize Socket.IO server
sio = socketio.AsyncServer(
    async_mode='asgi',
    cors_allowed_origins="*",  # Allow all origins for development
    ping_timeout=60,
    ping_interval=25
)

# Pydantic models
class ChatCreateRequest(BaseModel):
    client_provided_id: Optional[str] = None

class MessageRequest(BaseModel):
    chat_id: str
    content: str

class ChatResetRequest(BaseModel):
    chat_id: str
    client_provided_id: Optional[str] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    print("🚀 Starting Chatbot Backend...")

    # Initialize database
    await init_db()
    print("✅ Database initialized")

    # Run health checks
    health_results = await run_all_health_checks()
    if health_results["overall_status"] != "healthy":
        print("⚠️ Some systems are not healthy, but continuing startup...")

    # Initialize PDF service first
    app.state.pdf_service = PDFService()
    print("✅ PDF service initialized")

    # Initialize chat service with PDF service
    app.state.chat_service = ChatService(pdf_service=app.state.pdf_service)
    print("✅ Chat service initialized")

    print("🎉 Chatbot Backend is ready!")

    yield

    # Shutdown
    print("🛑 Shutting down Chatbot Backend...")
    await close_db()
    print("✅ Database connections closed")

# Create FastAPI app
app = FastAPI(
    title="Chatbot Backend",
    description="AI-powered chatbot with PDF processing and vector search",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins="*",  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount Socket.IO
socket_app = socketio.ASGIApp(sio, app)

# Socket.IO event handlers
@sio.event
async def connect(sid, environ):
    """Handle client connection"""
    print(f"Client {sid} connected")
    await sio.emit('connected', {'message': 'Connected to chatbot'}, room=sid)

@sio.event
async def disconnect(sid):
    """Handle client disconnection"""
    print(f"Client {sid} disconnected")

@sio.event
async def join_chat(sid, data):
    """Join chat room and get history"""
    try:
        chat_id = data.get('chatId')
        if not chat_id:
            await sio.emit('error', {'message': 'Chat ID is required'}, room=sid)
            return

        chat_service = app.state.chat_service

        # Validate chat ID
        if not chat_service.validate_chat_id(chat_id):
            await sio.emit('error', {'message': 'Invalid chat ID format'}, room=sid)
            return

        # Get chat history
        history = await chat_service.get_chat_history_cached(chat_id)
        await sio.emit('chat-history', history, room=sid)

    except Exception as e:
        print(f"Error joining chat: {e}")
        await sio.emit('error', {'message': 'Failed to join chat'}, room=sid)

@sio.event
async def send_message(sid, data):
    """Send message to chat"""
    try:
        chat_id = data.get('chatId')
        content = data.get('content')

        if not chat_id or not content:
            await sio.emit('error', {'message': 'Chat ID and content are required'}, room=sid)
            return

        chat_service = app.state.chat_service

        # Send message and get response
        result = await chat_service.send_message(chat_id, content)

        # Emit new messages
        await sio.emit('new-messages', [
            result['user_message'],
            result['assistant_message']
        ], room=sid)

    except Exception as e:
        print(f"Error sending message: {e}")
        await sio.emit('error', {'message': 'Failed to send message'}, room=sid)

@sio.event
async def typing_start(sid, data):
    """Handle typing start"""
    chat_id = data.get('chatId')
    if chat_id:
        await sio.emit('user-typing', True, room=sid)

@sio.event
async def typing_end(sid, data):
    """Handle typing end"""
    chat_id = data.get('chatId')
    if chat_id:
        await sio.emit('user-typing', False, room=sid)

@sio.event
async def reset_chat(sid, data):
    """Reset chat session"""
    try:
        old_chat_id = data.get('chatId')
        client_provided_id = data.get('clientProvidedId')

        if not old_chat_id:
            await sio.emit('error', {'message': 'Chat ID is required'}, room=sid)
            return

        chat_service = app.state.chat_service

        # Reset chat
        result = await chat_service.reset_chat(old_chat_id, client_provided_id)

        await sio.emit('chat-reset', result, room=sid)

    except Exception as e:
        print(f"Error resetting chat: {e}")
        await sio.emit('error', {'message': 'Failed to reset chat'}, room=sid)

# REST API Routes
@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "Chatbot Backend is running!", "status": "healthy"}

@app.get("/health")
@limiter.limit("100/15minutes")
async def health_check(request: Request):
    """Detailed health check"""
    try:
        health_results = await run_all_health_checks()
        return health_results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@app.post("/api/chat")
@limiter.limit("100/15minutes")
async def create_chat(request: Request, chat_request: ChatCreateRequest):
    """Create new chat session"""
    try:
        chat_service = app.state.chat_service
        chat_id = await chat_service.create_new_chat(chat_request.client_provided_id)

        return {
            "chat_id": chat_id,
            "message": "Chat created successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create chat: {str(e)}")

@app.get("/api/chat/{chat_id}/history")
@limiter.limit("100/15minutes")
async def get_chat_history_endpoint(request: Request, chat_id: str):
    """Get chat history"""
    try:
        chat_service = app.state.chat_service

        if not chat_service.validate_chat_id(chat_id):
            raise HTTPException(status_code=400, detail="Invalid chat ID format")

        history = await chat_service.get_chat_history_cached(chat_id)
        return {"messages": history}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get chat history: {str(e)}")

@app.post("/api/pdf/upload")
@limiter.limit("100/15minutes")
async def upload_pdf(request: Request, file: UploadFile = File(...)):
    """Upload PDF document"""
    try:
        pdf_service = app.state.pdf_service
        result = await pdf_service.upload_pdf(file)
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to upload PDF: {str(e)}")

@app.get("/api/documents")
@limiter.limit("100/15minutes")
async def list_documents(request: Request):
    """List uploaded documents"""
    try:
        pdf_service = app.state.pdf_service
        documents = pdf_service.list_documents()
        return {"documents": documents}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list documents: {str(e)}")

@app.delete("/api/documents/{document_id}")
@limiter.limit("100/15minutes")
async def delete_document(request: Request, document_id: str):
    """Delete document"""
    try:
        pdf_service = app.state.pdf_service
        success = await pdf_service.delete_document(document_id)
        if success:
            return {"message": "Document deleted successfully"}
        else:
            raise HTTPException(status_code=404, detail="Document not found")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete document: {str(e)}")

@app.post("/api/search")
@limiter.limit("100/15minutes")
async def search_documents(request: Request, query: dict):
    """Search documents"""
    try:
        search_query = query.get("query", "")
        if not search_query:
            raise HTTPException(status_code=400, detail="Query is required")

        pdf_service = app.state.pdf_service
        results = await pdf_service.search_documents(search_query, top_k=5)
        return {"results": results}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to search documents: {str(e)}")

# Add rate limiting error handler
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

if __name__ == "__main__":
    port = int(os.getenv("PORT", 3000))
    uvicorn.run(
        "main:socket_app",
        host="0.0.0.0",
        port=port,
        reload=True if os.getenv("NODE_ENV") == "development" else False
    )
