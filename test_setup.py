"""
Test script to verify the chatbot backend setup
"""
import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        import fastapi
        print("✅ FastAPI imported successfully")
        
        import uvicorn
        print("✅ Uvicorn imported successfully")
        
        import socketio
        print("✅ Socket.IO imported successfully")
        
        import sqlalchemy
        print("✅ SQLAlchemy imported successfully")
        
        import asyncpg
        print("✅ AsyncPG imported successfully")

        import google.generativeai as genai
        print("✅ Google Generative AI imported successfully")
        
        import faiss
        print("✅ FAISS imported successfully")
        
        import numpy
        print("✅ NumPy imported successfully")
        
        print("🎉 All imports successful!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

async def test_environment():
    """Test environment variables"""
    print("\n🔧 Testing environment variables...")
    
    required_vars = [
        "DB_HOST", "DB_PORT", "DB_USER", "DB_PASSWORD", "DB_NAME",
        "GEMINI_API_KEY"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
        else:
            print(f"✅ {var} is set")
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    else:
        print("🎉 All required environment variables are set!")
        return True

async def test_directories():
    """Test required directories"""
    print("\n📁 Testing directories...")
    
    required_dirs = [
        "data/docs",
        "vector_db"
    ]
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✅ Directory exists: {dir_path}")
        else:
            print(f"❌ Directory missing: {dir_path}")
            try:
                os.makedirs(dir_path, exist_ok=True)
                print(f"✅ Created directory: {dir_path}")
            except Exception as e:
                print(f"❌ Failed to create directory {dir_path}: {e}")
                return False
    
    print("🎉 All directories are ready!")
    return True

async def test_database_config():
    """Test database configuration"""
    print("\n🗄️ Testing database configuration...")
    
    try:
        from database import DATABASE_URL, async_engine
        print("✅ Database configuration loaded")
        
        # Test if we can create the engine
        if async_engine:
            print("✅ Database engine created successfully")
            return True
        else:
            print("❌ Failed to create database engine")
            return False
            
    except Exception as e:
        print(f"❌ Database configuration error: {e}")
        return False

async def test_chat_service():
    """Test chat service initialization"""
    print("\n💬 Testing chat service...")
    
    try:
        # Import without initializing (to avoid Redis/DB connections)
        from chat_service import ChatService
        print("✅ ChatService class imported successfully")
        return True
        
    except Exception as e:
        print(f"❌ ChatService error: {e}")
        return False

async def run_all_tests():
    """Run all tests"""
    print("🚀 Starting chatbot backend setup tests...\n")
    
    tests = [
        ("Imports", test_imports),
        ("Environment", test_environment),
        ("Directories", test_directories),
        ("Database Config", test_database_config),
        ("Chat Service", test_chat_service)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = await test_func()
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*50)
    print("TEST SUMMARY")
    print("="*50)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        icon = "✅" if result else "❌"
        print(f"{icon} {test_name}: {status}")
        if result:
            passed += 1
    
    print("="*50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Your chatbot backend is ready to run.")
        print("\nNext steps:")
        print("1. Ensure PostgreSQL and Redis are running")
        print("2. Run: python check_models.py (for full health checks)")
        print("3. Run: python main.py (to start the server)")
    else:
        print("⚠️ Some tests failed. Please fix the issues before running the server.")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(run_all_tests())
