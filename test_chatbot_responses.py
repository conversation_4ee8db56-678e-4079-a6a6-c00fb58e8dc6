"""
Test script to directly test chatbot responses without running the full server
"""
import asyncio
import os
from dotenv import load_dotenv
from chat_service import Cha<PERSON><PERSON>ervice
from pdf_service import PDFService

load_dotenv()

async def test_chatbot_responses():
    """Test chatbot responses directly"""
    print("🤖 Testing Chatbot Responses")
    print("=" * 50)
    
    # Check if API key is available
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ GEMINI_API_KEY not found in environment variables")
        print("Please set your GEMINI_API_KEY in the .env file")
        return
    
    try:
        # Initialize services
        print("🔧 Initializing services...")
        
        # Initialize PDF service (optional)
        pdf_service = None
        try:
            pdf_service = PDFService()
            print("✅ PDFService initialized")
        except Exception as e:
            print(f"⚠️ PDFService initialization failed: {e}")
            print("Continuing without PDF service...")
        
        # Initialize chat service
        chat_service = ChatService(pdf_service=pdf_service)
        print("✅ ChatService initialized")
        
        # Create a new chat
        print("\n📝 Creating new chat...")
        chat_id = await chat_service.create_chat()
        print(f"✅ Chat created with ID: {chat_id}")
        
        # Send welcome message
        print("\n👋 Sending welcome message...")
        welcome_message = await chat_service.send_welcome_message(chat_id)
        print(f"🤖 Welcome: {welcome_message['content']}")
        
        # Test 1: Send "hi"
        print("\n" + "="*50)
        print("TEST 1: Sending 'hi'")
        print("="*50)
        
        user_message_1 = "hi"
        print(f"👤 User: {user_message_1}")
        
        messages_1 = await chat_service.process_user_message(chat_id, user_message_1)
        user_msg_1 = messages_1[0]
        bot_msg_1 = messages_1[1]
        
        print(f"👤 Saved User Message: {user_msg_1['content']}")
        print(f"🤖 Bot Response: {bot_msg_1['content']}")
        
        # Test 2: Send "apa itu dpmptsp"
        print("\n" + "="*50)
        print("TEST 2: Sending 'apa itu dpmptsp'")
        print("="*50)
        
        user_message_2 = "apa itu dpmptsp"
        print(f"👤 User: {user_message_2}")
        
        messages_2 = await chat_service.process_user_message(chat_id, user_message_2)
        user_msg_2 = messages_2[0]
        bot_msg_2 = messages_2[1]
        
        print(f"👤 Saved User Message: {user_msg_2['content']}")
        print(f"🤖 Bot Response: {bot_msg_2['content']}")
        
        # Show chat history
        print("\n" + "="*50)
        print("CHAT HISTORY")
        print("="*50)
        
        history = await chat_service.get_chat_history_cached(chat_id)
        for i, msg in enumerate(history, 1):
            role_icon = "👤" if msg['role'] == 'user' else "🤖"
            print(f"{i}. {role_icon} {msg['role'].title()}: {msg['content']}")
        
        print(f"\n✅ Total messages in history: {len(history)}")
        print("🎉 Chatbot response test completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

async def test_gemini_service_directly():
    """Test GeminiService directly"""
    print("\n🧠 Testing GeminiService Directly")
    print("=" * 50)
    
    try:
        from gemini_service import GeminiService
        
        gemini_service = GeminiService()
        print("✅ GeminiService initialized")
        
        # Test 1: Welcome message
        welcome = await gemini_service.generate_welcome_message()
        print(f"👋 Welcome Message: {welcome}")
        
        # Test 2: Response without context
        response1 = await gemini_service.generate_response("hi")
        print(f"🤖 Response to 'hi': {response1}")
        
        # Test 3: Response with mock context
        mock_context = {
            "history": [
                {"role": "assistant", "content": welcome},
                {"role": "user", "content": "hi"}
            ],
            "pdf_context": {
                "chunks": [
                    {
                        "content": "DPMPTSP adalah Dinas Penanaman Modal dan Pelayanan Terpadu Satu Pintu yang bertugas memberikan layanan perizinan dan investasi di Kota Pekanbaru.",
                        "documentId": "doc1"
                    }
                ],
                "documentNames": {
                    "doc1": "Panduan DPMPTSP"
                }
            }
        }
        
        response2 = await gemini_service.generate_response("apa itu dpmptsp", mock_context)
        print(f"🤖 Response to 'apa itu dpmptsp' with context: {response2}")
        
        print("✅ GeminiService direct test completed!")
        
    except Exception as e:
        print(f"❌ GeminiService test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 Starting Chatbot Response Tests")
    print("This will test the chatbot responses without running the full server")
    print()
    
    # Run the tests
    asyncio.run(test_gemini_service_directly())
    asyncio.run(test_chatbot_responses())
