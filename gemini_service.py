"""
Gemini Service - Optimized AI service for chatbot responses
Based on the TypeScript implementation with Python optimizations
"""
import os
import asyncio
from typing import Dict, Optional, Any
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold, GenerationConfig
from dotenv import load_dotenv

load_dotenv()


class GeminiService:
    """Optimized Gemini AI service for fast, high-quality responses"""

    def __init__(self):
        # Configure Gemini AI
        genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
        
        # Define system prompt
        self.system_prompt = """Peran:
Anda adalah Asisten Virtual DPMPTSP yang bertugas memberikan informasi, panduan, dan bantuan terkait layanan perizinan dan investasi. Anda harus responsif, informatif, dan menggunakan bahasa yang jelas serta mudah dipahami oleh masyarakat umum.

Tanggung Jawab Utama:
1. Memberikan Informasi Umum tentang DPMPTSP dan layanannya
2. Memberikan panduan lengkap tentang layanan perizinan
3. Menggunakan bahasa yang sopan, formal, namun tetap ramah

Aturan Penting:
1. JANGAN PERNAH menyebutkan "berdasarkan dokumen" atau "dokumen yang diberikan" dalam jawaban Anda
2. Jawab seolah-olah informasi ini adalah pengetahuan umum Anda tentang DPMPTSP
3. Jika Anda tidak memiliki informasi yang cukup, sarankan pengguna untuk menghubungi DPMPTSP melalui email atau website resmi"""

        # Use the fastest model available - gemini-1.5-flash is optimized for speed
        self.model = genai.GenerativeModel(
            model_name="gemini-1.5-flash",
            system_instruction=self.system_prompt
        )

        # Safety settings
        self.safety_settings = [
            {
                "category": HarmCategory.HARM_CATEGORY_HARASSMENT,
                "threshold": HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            },
            {
                "category": HarmCategory.HARM_CATEGORY_HATE_SPEECH,
                "threshold": HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            },
            {
                "category": HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
                "threshold": HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            },
            {
                "category": HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                "threshold": HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            },
        ]

        # Generation config optimized for speed
        self.generation_config = GenerationConfig(
            temperature=0.5,  # Lower temperature for faster, more deterministic responses
            top_k=20,  # Lower topK for faster generation
            top_p=0.8,  # Lower topP for faster generation
            max_output_tokens=512,  # Reduced max tokens to speed up generation
        )

        print("✅ GeminiService initialized")

    def _check_response_quality(self, response: str) -> bool:
        """Check if the response meets quality standards"""
        # Check if the response contains the "no information" message
        if "tidak memiliki informasi yang cukup" in response:
            return True  # This is an acceptable "no info" response

        # Check minimum length for a substantive response
        if len(response) < 50:
            return False

        return True

    async def generate_response(
        self,
        user_message: str,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Generate AI response with PDF context and conversation history
        
        Args:
            user_message: The user's message
            context: Optional context containing:
                - history: List of previous messages
                - pdf_context: PDF search results with chunks and document names
        
        Returns:
            Generated response string
        """
        try:
            # Initialize a new chat for each request to avoid state issues
            chat = self.model.start_chat(history=[])

            # Build the effective message with context
            effective_message = user_message

            # If we have PDF context, include it in the prompt
            if (context and 
                context.get("pdf_context") and 
                context["pdf_context"].get("chunks") and 
                len(context["pdf_context"]["chunks"]) > 0):
                
                # Limit to maximum 3 chunks to reduce context size
                chunks = context["pdf_context"]["chunks"][:3]
                
                # Format the PDF context more concisely
                context_chunks = []
                for chunk in chunks:
                    # Truncate chunk content if too long
                    content = chunk.get("content", "")
                    if len(content) > 300:
                        content = content[:300] + "..."
                    context_chunks.append(content)

                context_text = "\n".join(context_chunks)
                
                print(f"Using {len(chunks)} PDF chunks (limited) for query: \"{user_message}\"")

                # Create a more concise prompt without mentioning documents
                effective_message = f"""
Anda adalah asisten virtual DPMPTSP. Gunakan HANYA informasi berikut untuk menjawab:
{context_text}

Pertanyaan: {user_message}

PENTING:
1. JANGAN PERNAH menyebutkan "berdasarkan dokumen" atau "dokumen yang diberikan" dalam jawaban Anda
2. Jawab seolah-olah informasi ini adalah pengetahuan umum Anda tentang DPMPTSP
3. Jika informasi di atas TIDAK CUKUP untuk menjawab pertanyaan dengan baik, berikan respons: "Maaf, saya tidak memiliki informasi yang cukup untuk menjawab pertanyaan tersebut. Silakan hubungi DPMPTSP melalui <NAME_EMAIL> atau kunjungi website resmi kami."
4. JANGAN GUNAKAN pengetahuan umum Anda untuk menjawab pertanyaan - HANYA gunakan informasi yang diberikan di atas
"""
            else:
                print(f"No PDF context available for query: \"{user_message}\"")
                # If no PDF context is available, return a standard "no information" response
                return "Maaf, saya tidak memiliki informasi yang cukup untuk menjawab pertanyaan tersebut. Silakan hubungi DPMPTSP melalui <NAME_EMAIL> atau kunjungi website resmi kami untuk informasi lebih lanjut."

            # Add conversation history if available (limit to last 3 messages)
            if context and context.get("history"):
                recent_history = context["history"][-3:]
                history_text = ""
                for msg in recent_history:
                    role = msg.get("role", "")
                    content = msg.get("content", "")
                    if role in ["user", "assistant"]:
                        history_text += f"{role}: {content}\n"
                
                if history_text:
                    effective_message = f"Riwayat percakapan:\n{history_text}\n\n{effective_message}"

            print("Sending request to Gemini...")
            
            # Generate response using asyncio.to_thread for non-blocking execution
            response = await asyncio.to_thread(
                chat.send_message,
                effective_message,
                safety_settings=self.safety_settings,
                generation_config=self.generation_config
            )
            
            response_text = response.text.strip()

            # Check response quality
            if not self._check_response_quality(response_text):
                return "Maaf, saya tidak memiliki informasi yang cukup untuk menjawab pertanyaan tersebut. Silakan hubungi DPMPTSP melalui <NAME_EMAIL> atau kunjungi website resmi kami untuk informasi lebih lanjut."

            return response_text

        except Exception as e:
            print(f"Error generating AI response: {e}")
            if hasattr(e, 'message'):
                print(f"Error details: {e.message}")
            raise Exception("Failed to generate AI response")

    async def generate_welcome_message(self) -> str:
        """
        Generate a welcome message
        Using a pre-defined message for speed instead of API call
        """
        return "Halo! Saya asisten virtual DPMPTSP, siap membantu Anda dengan informasi seputar layanan perizinan dan investasi di Kota Pekanbaru. Silakan ajukan pertanyaan Anda!"
