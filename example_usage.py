"""
Example usage of the chatbot backend API
"""
import requests
import json

# Configuration
BASE_URL = "http://localhost:3001"

def test_health_check():
    """Test health check endpoint"""
    print("🔍 Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ Health check passed")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def create_chat():
    """Create a new chat session"""
    print("\n💬 Creating new chat...")
    try:
        response = requests.post(f"{BASE_URL}/api/chat", json={})
        if response.status_code == 200:
            data = response.json()
            chat_id = data["chat_id"]
            print(f"✅ Chat created: {chat_id}")
            return chat_id
        else:
            print(f"❌ Failed to create chat: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Chat creation error: {e}")
        return None

def upload_pdf(file_path):
    """Upload a PDF file"""
    print(f"\n📄 Uploading PDF: {file_path}")
    try:
        with open(file_path, 'rb') as f:
            files = {'file': (file_path, f, 'application/pdf')}
            response = requests.post(f"{BASE_URL}/api/pdf/upload", files=files)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ PDF uploaded successfully")
            print(f"   Document ID: {data['document_id']}")
            print(f"   Chunks created: {data['chunks_created']}")
            return data['document_id']
        else:
            print(f"❌ Failed to upload PDF: {response.status_code}")
            print(f"   Error: {response.text}")
            return None
    except Exception as e:
        print(f"❌ PDF upload error: {e}")
        return None

def list_documents():
    """List all uploaded documents"""
    print("\n📚 Listing documents...")
    try:
        response = requests.get(f"{BASE_URL}/api/documents")
        if response.status_code == 200:
            data = response.json()
            documents = data["documents"]
            print(f"✅ Found {len(documents)} documents:")
            for doc in documents:
                print(f"   - {doc['filename']} ({doc['chunks_count']} chunks)")
            return documents
        else:
            print(f"❌ Failed to list documents: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Document listing error: {e}")
        return []

def search_documents(query):
    """Search documents"""
    print(f"\n🔍 Searching documents for: '{query}'")
    try:
        response = requests.post(f"{BASE_URL}/api/search", json={"query": query})
        if response.status_code == 200:
            data = response.json()
            results = data["results"]
            print(f"✅ Found {len(results)} relevant chunks:")
            for i, result in enumerate(results[:3]):  # Show top 3
                print(f"   {i+1}. From {result['document_name']}")
                print(f"      Similarity: {result['similarity_score']:.3f}")
                print(f"      Content: {result['content'][:100]}...")
            return results
        else:
            print(f"❌ Failed to search documents: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Document search error: {e}")
        return []

def get_chat_history(chat_id):
    """Get chat history"""
    print(f"\n📜 Getting chat history for: {chat_id}")
    try:
        response = requests.get(f"{BASE_URL}/api/chat/{chat_id}/history")
        if response.status_code == 200:
            data = response.json()
            messages = data["messages"]
            print(f"✅ Found {len(messages)} messages:")
            for msg in messages:
                print(f"   {msg['role']}: {msg['content'][:100]}...")
            return messages
        else:
            print(f"❌ Failed to get chat history: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Chat history error: {e}")
        return []

def main():
    """Main example function"""
    print("🚀 Chatbot Backend API Example")
    print("=" * 50)
    
    # Test health check
    if not test_health_check():
        print("❌ Server is not healthy, stopping...")
        return
    
    # Create chat
    chat_id = create_chat()
    if not chat_id:
        print("❌ Failed to create chat, stopping...")
        return
    
    # List existing documents
    documents = list_documents()
    
    # Example PDF upload (you need to provide a real PDF file)
    # pdf_file = "example.pdf"  # Replace with actual PDF file path
    # if os.path.exists(pdf_file):
    #     document_id = upload_pdf(pdf_file)
    #     if document_id:
    #         # Search documents
    #         search_documents("perizinan")
    #         search_documents("investasi")
    
    # Get chat history
    get_chat_history(chat_id)
    
    print("\n🎉 Example completed!")
    print("\nNext steps:")
    print("1. Upload a PDF file using the /api/pdf/upload endpoint")
    print("2. Use WebSocket to send messages and get AI responses")
    print("3. The AI will automatically search uploaded documents for context")

if __name__ == "__main__":
    main()
