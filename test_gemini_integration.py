"""
Test script for the new GeminiService integration
"""
import asyncio
import os
from dotenv import load_dotenv
from gemini_service import GeminiService

load_dotenv()

async def test_gemini_service():
    """Test the GeminiService functionality"""
    print("🧪 Testing GeminiService...")
    
    # Check if API key is available
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ GEMINI_API_KEY not found in environment variables")
        return
    
    try:
        # Initialize service
        gemini_service = GeminiService()
        print("✅ GeminiService initialized successfully")
        
        # Test welcome message
        welcome = await gemini_service.generate_welcome_message()
        print(f"✅ Welcome message: {welcome}")
        
        # Test basic response without context
        test_message = "Apa itu DPMPTSP?"
        response = await gemini_service.generate_response(test_message)
        print(f"✅ Basic response: {response}")
        
        # Test response with mock PDF context
        mock_context = {
            "history": [
                {"role": "user", "content": "Halo"},
                {"role": "assistant", "content": "Halo! Ada yang bisa saya bantu?"}
            ],
            "pdf_context": {
                "chunks": [
                    {
                        "content": "DPMPTSP adalah Dinas <PERSON>man <PERSON> dan <PERSON>yanan Terpadu Satu Pintu yang bertugas memberikan layanan perizinan dan investasi.",
                        "documentId": "doc1"
                    }
                ],
                "documentNames": {
                    "doc1": "Panduan DPMPTSP"
                }
            }
        }
        
        response_with_context = await gemini_service.generate_response(
            "Jelaskan tentang DPMPTSP", 
            mock_context
        )
        print(f"✅ Response with context: {response_with_context}")
        
        print("🎉 All tests passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_gemini_service())
