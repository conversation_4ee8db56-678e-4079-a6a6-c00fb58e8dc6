"""
Test script for the ChatService with new GeminiService integration
"""
import asyncio
import os
from dotenv import load_dotenv
from chat_service import ChatService
from pdf_service import PDFService

load_dotenv()

async def test_chat_service():
    """Test the ChatService with GeminiService integration"""
    print("🧪 Testing ChatService with GeminiService...")
    
    # Check if API key is available
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ GEMINI_API_KEY not found in environment variables")
        return
    
    try:
        # Initialize PDF service (optional)
        pdf_service = None
        try:
            pdf_service = PDFService()
            print("✅ PDFService initialized")
        except Exception as e:
            print(f"⚠️ PDFService initialization failed: {e}")
            print("Continuing without PDF service...")
        
        # Initialize chat service
        chat_service = ChatService(pdf_service=pdf_service)
        print("✅ ChatService initialized successfully")
        
        # Test welcome message generation
        welcome_msg = await chat_service._get_welcome_message()
        print(f"✅ Welcome message generated: {welcome_msg['content'][:100]}...")
        
        # Test AI response generation without PDF context
        test_history = [
            {"role": "assistant", "content": "Halo! Saya asisten virtual DPMPTSP."},
            {"role": "user", "content": "Halo"}
        ]
        
        response = await chat_service._generate_ai_response("Apa itu DPMPTSP?", test_history)
        print(f"✅ AI response generated: {response[:100]}...")
        
        print("🎉 ChatService integration test passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_chat_service())
