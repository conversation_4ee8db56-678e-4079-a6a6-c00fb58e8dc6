"""
Database configuration and models for the chatbot backend
"""
import os
import uuid
from datetime import datetime
from typing import Optional, List
from sqlalchemy import Column, String, Text, DateTime, create_engine, UUID, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker
from sqlalchemy.sql import text
from dotenv import load_dotenv

load_dotenv()

# Database configuration
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = os.getenv("DB_PORT", "5432")
DB_USER = os.getenv("DB_USER")
DB_PASSWORD = os.getenv("DB_PASSWORD")
DB_NAME = os.getenv("DB_NAME", "chatbot")
DB_POOL_SIZE = int(os.getenv("DB_POOL_SIZE", 20))

# Create database URLs
DATABASE_URL = f"postgresql+asyncpg://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
SYNC_DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# Create async engine
async_engine = create_async_engine(
    DATABASE_URL,
    pool_size=DB_POOL_SIZE,
    max_overflow=0,
    echo=True if os.getenv("NODE_ENV") == "development" else False
)

# Create sync engine for migrations
sync_engine = create_engine(SYNC_DATABASE_URL)

# Create session makers
AsyncSessionLocal = async_sessionmaker(
    bind=async_engine,
    class_=AsyncSession,
    expire_on_commit=False
)

SessionLocal = sessionmaker(bind=sync_engine)

# Base class for models
Base = declarative_base()

# Database Models matching your exact schema
class Chat(Base):
    """Chat session model"""
    __tablename__ = "chats"
    __table_args__ = {'schema': 'chatbot'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Message(Base):
    """Chat message model"""
    __tablename__ = "messages"
    __table_args__ = (
        Index('idx_messages_chat_id', 'chat_id'),
        Index('idx_messages_timestamp', 'timestamp'),
        {'schema': 'chatbot'}
    )

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    chat_id = Column(UUID(as_uuid=True), nullable=False)
    content = Column(Text, nullable=False)
    role = Column(String(10), nullable=False)  # 'user' or 'assistant'
    timestamp = Column(DateTime, default=datetime.utcnow)

# Database utility functions
async def get_async_session():
    """Get async database session"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

def get_sync_session():
    """Get sync database session"""
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()

async def init_db():
    """Initialize database with exact schema"""
    try:
        async with async_engine.begin() as conn:
            # Create schema and tables with separate SQL commands
            # Create schema
            await conn.execute(text("CREATE SCHEMA IF NOT EXISTS chatbot;"))

            # Create chats table
            await conn.execute(text("""
                CREATE TABLE IF NOT EXISTS chatbot.chats (
                  id UUID PRIMARY KEY,
                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """))

            # Create messages table
            await conn.execute(text("""
                CREATE TABLE IF NOT EXISTS chatbot.messages (
                  id UUID PRIMARY KEY,
                  chat_id UUID REFERENCES chatbot.chats(id) ON DELETE CASCADE,
                  content TEXT NOT NULL,
                  role VARCHAR(10) NOT NULL CHECK (role IN ('user', 'assistant')),
                  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """))

            # Create indexes
            await conn.execute(text("CREATE INDEX IF NOT EXISTS idx_messages_chat_id ON chatbot.messages(chat_id);"))
            await conn.execute(text("CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON chatbot.messages(timestamp);"))

        print("✅ Database schema created successfully")
    except Exception as e:
        print(f"❌ Error creating database schema: {e}")
        raise

async def close_db():
    """Close database connections"""
    try:
        await async_engine.dispose()
        print("✅ Database connections closed")
    except Exception as e:
        print(f"❌ Error closing database connections: {e}")

# Database helper functions
async def create_chat() -> str:
    """Create new chat session"""
    async with AsyncSessionLocal() as session:
        chat_id = uuid.uuid4()
        await session.execute(
            text("INSERT INTO chatbot.chats (id) VALUES (:id)"),
            {"id": chat_id}
        )
        await session.commit()
        return str(chat_id)

async def chat_exists(chat_id: str) -> bool:
    """Check if chat exists"""
    try:
        chat_uuid = uuid.UUID(chat_id)
        async with AsyncSessionLocal() as session:
            result = await session.execute(
                text("SELECT id FROM chatbot.chats WHERE id = :chat_id"),
                {"chat_id": chat_uuid}
            )
            return result.fetchone() is not None
    except (ValueError, TypeError):
        return False

async def save_message(chat_id: str, content: str, role: str) -> dict:
    """Save message to database"""
    async with AsyncSessionLocal() as session:
        message_id = uuid.uuid4()
        timestamp = datetime.utcnow()

        await session.execute(
            text("""
            INSERT INTO chatbot.messages (id, chat_id, content, role, timestamp)
            VALUES (:id, :chat_id, :content, :role, :timestamp)
            """),
            {
                "id": message_id,
                "chat_id": uuid.UUID(chat_id),
                "content": content[:4096],  # Truncate to 4096 chars
                "role": role,
                "timestamp": timestamp
            }
        )
        await session.commit()

        return {
            "id": str(message_id),
            "chat_id": chat_id,
            "content": content[:4096],
            "role": role,
            "timestamp": timestamp.isoformat()
        }

async def get_chat_history(chat_id: str, limit: int = 10) -> List[dict]:
    """Get chat history for a chat"""
    try:
        chat_uuid = uuid.UUID(chat_id)
        async with AsyncSessionLocal() as session:
            result = await session.execute(
                text("""
                SELECT id, chat_id, content, role, timestamp
                FROM chatbot.messages
                WHERE chat_id = :chat_id
                ORDER BY timestamp DESC
                LIMIT :limit
                """),
                {"chat_id": chat_uuid, "limit": limit}
            )
            messages = result.fetchall()

            return [
                {
                    "id": str(msg.id),
                    "chat_id": str(msg.chat_id),
                    "content": msg.content,
                    "role": msg.role,
                    "timestamp": msg.timestamp.isoformat()
                }
                for msg in reversed(messages)  # Reverse to get chronological order
            ]
    except (ValueError, TypeError):
        return []

async def update_chat_timestamp(chat_id: str):
    """Update chat's updated_at timestamp"""
    try:
        chat_uuid = uuid.UUID(chat_id)
        async with AsyncSessionLocal() as session:
            await session.execute(
                text("UPDATE chatbot.chats SET updated_at = CURRENT_TIMESTAMP WHERE id = :chat_id"),
                {"chat_id": chat_uuid}
            )
            await session.commit()
    except (ValueError, TypeError):
        pass
