"""
Chat Service - Core business logic for chatbot functionality
"""
import uuid
import time
from datetime import datetime
from typing import List, Optional
from dotenv import load_dotenv

from database import (
    create_chat, chat_exists, save_message, get_chat_history,
    update_chat_timestamp
)
from gemini_service import GeminiService

load_dotenv()

class ChatService:
    """Main chat service handling all chat operations"""

    def __init__(self, pdf_service=None):
        # Initialize in-memory cache for chat history
        self.cache = {}  # {chat_id: {"data": history, "timestamp": time}}

        # Initialize Gemini AI service
        self.gemini_service = GeminiService()

        # PDF service for document search
        self.pdf_service = pdf_service

        # Configuration
        self.cache_ttl = 3600  # 1 hour
        self.max_history_messages = 10
        self.max_content_length = 4096

        print("✅ ChatService initialized")
    

    
    async def create_new_chat(self, client_provided_id: Optional[str] = None) -> str:
        """Create new chat session"""
        try:
            # Validate client provided ID if given
            if client_provided_id:
                try:
                    uuid.UUID(client_provided_id)
                    # Check if chat already exists
                    if await chat_exists(client_provided_id):
                        return client_provided_id
                    else:
                        # Create chat with provided ID
                        chat_id = client_provided_id
                except ValueError:
                    # Invalid UUID, create new one
                    chat_id = await create_chat()
            else:
                # Create new chat
                chat_id = await create_chat()
            
            # Cache welcome message
            welcome_message = await self._get_welcome_message()
            await self._cache_chat_history(chat_id, [welcome_message])
            
            return chat_id
            
        except Exception as e:
            print(f"Error creating chat: {e}")
            raise
    
    async def _get_welcome_message(self) -> dict:
        """Get welcome message for new chats"""
        welcome_content = await self.gemini_service.generate_welcome_message()
        return {
            "id": str(uuid.uuid4()),
            "role": "assistant",
            "content": welcome_content,
            "timestamp": datetime.now().isoformat()
        }
    
    async def get_chat_history_cached(self, chat_id: str) -> List[dict]:
        """Get chat history with Redis caching"""
        try:
            # Try to get from cache first
            cached_history = await self._get_cached_chat_history(chat_id)
            if cached_history:
                return cached_history
            
            # Get from database
            history = await get_chat_history(chat_id, self.max_history_messages)
            
            # Cache the result
            await self._cache_chat_history(chat_id, history)
            
            return history
            
        except Exception as e:
            print(f"Error getting chat history: {e}")
            return []
    
    async def send_message(self, chat_id: str, content: str) -> dict:
        """Process user message and generate AI response"""
        try:
            # Validate chat exists
            if not await chat_exists(chat_id):
                raise ValueError("Chat not found")
            
            # Truncate content if too long
            content = content[:self.max_content_length]
            
            # Save user message
            user_message = await save_message(chat_id, content, "user")
            
            # Get chat history for context
            history = await self.get_chat_history_cached(chat_id)
            
            # Generate AI response
            ai_response = await self._generate_ai_response(content, history)
            
            # Save AI response
            assistant_message = await save_message(chat_id, ai_response, "assistant")
            
            # Update chat timestamp
            await update_chat_timestamp(chat_id)
            
            # Update cache
            updated_history = history + [user_message, assistant_message]
            await self._cache_chat_history(chat_id, updated_history[-self.max_history_messages:])
            
            return {
                "user_message": user_message,
                "assistant_message": assistant_message
            }
            
        except Exception as e:
            print(f"Error processing message: {e}")
            raise
    
    async def _generate_ai_response(self, user_message: str, history: List[dict]) -> str:
        """Generate AI response using GeminiService with PDF context"""
        try:
            # Search for relevant documents if PDF service is available
            pdf_context = None
            if self.pdf_service:
                search_results = await self.pdf_service.search_documents(user_message, top_k=3)
                if search_results:
                    # Convert search results to the format expected by GeminiService
                    chunks = []
                    document_names = {}

                    for doc in search_results:
                        chunks.append({
                            "content": doc.get("content", ""),
                            "documentId": doc.get("document_id", "")
                        })
                        if doc.get("document_id") and doc.get("document_name"):
                            document_names[doc["document_id"]] = doc["document_name"]

                    pdf_context = {
                        "chunks": chunks,
                        "documentNames": document_names
                    }

            # Prepare context for GeminiService
            context = {
                "history": history,
                "pdf_context": pdf_context
            }

            # Generate response using GeminiService
            response = await self.gemini_service.generate_response(user_message, context)
            return response

        except Exception as e:
            print(f"Error generating AI response: {e}")
            return "Maaf, terjadi kesalahan dalam memproses permintaan Anda. Silakan coba lagi atau hubungi kantor DPMPTSP untuk bantuan langsung."
    
    async def reset_chat(self, old_chat_id: str, new_chat_id: Optional[str] = None) -> dict:
        """Reset chat session"""
        try:
            # Create new chat
            new_id = await self.create_new_chat(new_chat_id)
            
            # Clear old chat cache
            await self._clear_chat_cache(old_chat_id)
            
            return {
                "old_chat_id": old_chat_id,
                "new_chat_id": new_id
            }
            
        except Exception as e:
            print(f"Error resetting chat: {e}")
            raise
    
    # In-memory caching methods
    async def _cache_chat_history(self, chat_id: str, history: List[dict]):
        """Cache chat history in memory"""
        try:
            current_time = time.time()
            self.cache[chat_id] = {
                "data": history,
                "timestamp": current_time
            }
            # Clean up expired entries
            await self._cleanup_expired_cache()
        except Exception as e:
            print(f"Error caching chat history: {e}")

    async def _get_cached_chat_history(self, chat_id: str) -> Optional[List[dict]]:
        """Get cached chat history from memory"""
        try:
            if chat_id not in self.cache:
                return None

            cached_entry = self.cache[chat_id]
            current_time = time.time()

            # Check if cache entry is still valid
            if current_time - cached_entry["timestamp"] > self.cache_ttl:
                del self.cache[chat_id]
                return None

            return cached_entry["data"]
        except Exception as e:
            print(f"Error getting cached chat history: {e}")
            return None

    async def _clear_chat_cache(self, chat_id: str):
        """Clear chat cache"""
        try:
            if chat_id in self.cache:
                del self.cache[chat_id]
        except Exception as e:
            print(f"Error clearing chat cache: {e}")

    async def _cleanup_expired_cache(self):
        """Clean up expired cache entries"""
        try:
            current_time = time.time()
            expired_keys = [
                chat_id for chat_id, entry in self.cache.items()
                if current_time - entry["timestamp"] > self.cache_ttl
            ]
            for key in expired_keys:
                del self.cache[key]
        except Exception as e:
            print(f"Error cleaning up cache: {e}")
    
    def validate_chat_id(self, chat_id: str) -> bool:
        """Validate chat ID format"""
        try:
            uuid.UUID(chat_id)
            return True
        except (ValueError, TypeError):
            return False
